// path: lib/core/widgets/loading_overlay.dart
import 'package:flutter/material.dart';

class LoadingOverlay extends StatelessWidget {
  final bool show;
  final Widget child;
  const LoadingOverlay({super.key, required this.show, required this.child});
  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      child,
      if (show)
        Positioned.fill(
          child: ColoredBox(
            color: Colors.black38,
            child: Center(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(color: Theme.of(context).colorScheme.surface, borderRadius: BorderRadius.circular(12)),
                child: const Row(mainAxisSize: MainAxisSize.min, children: [
                  SizedBox(height: 20, width: 20, child: CircularProgressIndicator(strokeWidth: 2)),
                  SizedBox(width: 12),
                  Text('Yuklanmoqda...'),
                ]),
              ),
            ),
          ),
        ),
    ]);
  }
}
