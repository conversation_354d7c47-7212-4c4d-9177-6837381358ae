// path: lib/core/widgets/empty_state.dart
import 'package:flutter/material.dart';
class EmptyState extends StatelessWidget {
  final String message;
  final VoidCallback? onAction;
  final String? actionLabel;
  const EmptyState({super.key, required this.message, this.onAction, this.actionLabel});
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(mainAxisSize: MainAxisSize.min, children: [
        const Icon(Icons.energy_savings_leaf_outlined, size: 64),
        const SizedBox(height: 8),
        Text(message, textAlign: TextAlign.center),
        if (onAction != null) ...[
          const SizedBox(height: 12),
          OutlinedButton(onPressed: onAction, child: Text(actionLabel ?? 'Qayta urinish')),
        ],
      ]),
    );
  }
}
