// path: lib/core/network/interceptors/retry_interceptor.dart
import 'dart:math';
import 'package:dio/dio.dart';

class RetryInterceptor extends Interceptor {
  final Dio dio;
  RetryInterceptor(this.dio);

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final shouldRetry = err.type != DioExceptionType.cancel &&
        (err.type == DioExceptionType.connectionTimeout ||
         err.type == DioExceptionType.unknown ||
         (err.response?.statusCode ?? 0) >= 500);

    if (!shouldRetry) return handler.next(err);

    int attempt = (err.requestOptions.extra['__retry_attempt'] ?? 0) + 1;
    if (attempt > 3) return handler.next(err);

    final delayMs = min(2000 * pow(2, attempt - 1).toInt(), 8000);
    await Future.delayed(Duration(milliseconds: delayMs));
    final opts = err.requestOptions..extra['__retry_attempt'] = attempt;
    try {
      final res = await dio.fetch(opts);
      return handler.resolve(res);
    } catch (e) {
      return handler.next(err);
    }
  }
}
