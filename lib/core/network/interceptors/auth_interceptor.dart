// path: lib/core/network/interceptors/auth_interceptor.dart
import 'package:dio/dio.dart';

class AuthInterceptor extends Interceptor {
  final Future<String?> Function() getAccessToken;
  final Future<bool> Function() tryRefresh;
  AuthInterceptor({required this.getAccessToken, required this.tryRefresh});

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final token = await getAccessToken();
    if (token != null && token.isNotEmpty) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      final refreshed = await tryRefresh();
      if (refreshed) {
        final req = err.requestOptions;
        final clone = await _retryRequest(req);
        return handler.resolve(clone);
      }
    }
    handler.next(err);
  }

  Future<Response<dynamic>> _retryRequest(RequestOptions req) async {
    final dio = Dio();
    dio.options.baseUrl = req.baseUrl;
    dio.options.connectTimeout = req.connectTimeout;
    dio.options.receiveTimeout = req.receiveTimeout;
    return dio.fetch(req);
  }
}
