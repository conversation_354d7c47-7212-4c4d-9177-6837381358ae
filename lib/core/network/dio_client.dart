// path: lib/core/network/dio_client.dart
import 'package:dio/dio.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import '../env/env.dart';
import 'interceptors/auth_interceptor.dart';
import 'interceptors/retry_interceptor.dart';

class DioClient {
  static Dio build({
    required Future<String?> Function() getAccessToken,
    required Future<String?> Function() getRefreshToken,
    required Future<bool> Function() tryRefresh,
  }) {
    final dio = Dio(BaseOptions(baseUrl: Env.apiBaseUrl, connectTimeout: const Duration(seconds: 15), receiveTimeout: const Duration(seconds: 30)));
    dio.interceptors.add(AuthInterceptor(getAccessToken: getAccessToken, tryRefresh: tryRefresh));
    dio.interceptors.add(RetryInterceptor(dio));
    dio.interceptors.add(PrettyDioLogger(requestHeader: true, requestBody: false, responseBody: false));
    return dio;
  }
}
