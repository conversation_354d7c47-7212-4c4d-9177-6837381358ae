// path: lib/core/error/app_exception.dart
class AppException implements Exception {
  final String message;
  final int? statusCode;
  AppException(this.message, {this.statusCode});
  @override
  String toString() => 'AppException($statusCode): $message';

  static AppException network([String m = 'Network error']) => AppException(m);
  static AppException auth([String m = 'Auth error']) => AppException(m, statusCode: 401);
  static AppException server([String m = 'Server error']) => AppException(m, statusCode: 500);
  static AppException validation([String m = 'Validation error']) => AppException(m, statusCode: 422);
  static AppException offline([String m = 'Offline']) => AppException(m);
}
