// path: lib/core/utils/validators.dart
class Validators {
  static String? requiredField(String? v) => (v == null || v.trim().isEmpty) ? 'Majburiy' : null;
  static String? phone(String? v) {
    if (v == null || v.trim().isEmpty) return 'Telefon kiritilsin';
    final p = RegExp(r'^\+?\d{9,15}$');
    return p.hasMatch(v) ? null : 'Telefon notogri';
  }
  static String? passport(String? v) {
    if (v == null || v.trim().isEmpty) return null;
    final p = RegExp(r'^[A-Z]{2}\d{7}$');
    return p.hasMatch(v) ? null : 'Passport notogri (*********)';
  }
  static String? qty(String? v) {
    final n = int.tryParse(v ?? '');
    if (n == null || n < 1) return 'Qty ≥ 1';
    return null;
  }
}
