// path: lib/app.dart
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'router/app_router.dart';
import 'theme/app_theme.dart';

enum AppFlavor { dev, prod }

class MobilKochatApp extends StatelessWidget {
  final AppFlavor flavor;
  const MobilKochatApp({super.key, required this.flavor});

  @override
  Widget build(BuildContext context) {
    final router = createRouter(flavor);
    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      title: "Mobil Ko'chat Worker",
      theme: AppTheme.light,
      darkTheme: AppTheme.dark,
      routerConfig: router,
      locale: const Locale('uz'),
      supportedLocales: const [Locale('uz'), Locale('ru')],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ],
    );
  }
}
