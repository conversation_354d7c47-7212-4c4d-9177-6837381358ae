// path: lib/main_prod.dart
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'app.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: ".env.prod");
  runApp(const ProviderScope(child: <PERSON><PERSON>KochatApp(flavor: AppFlavor.prod)));
}
