// path: lib/features/sync/services/sync_service.dart
import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../../orders/repo/orders_repo.dart';

class SyncService {
  final OrdersRepo repo;
  StreamSubscription? _sub;

  SyncService(this.repo);

  void start() {
    _sub = Connectivity().onConnectivityChanged.listen((_) => _trySync());
    _trySync();
  }

  void dispose() {
    _sub?.cancel();
  }

  Future<void> _trySync() async {
    final con = await Connectivity().checkConnectivity();
    if (con.contains(ConnectivityResult.none)) return;
    final queue = await repo.getQueue();
    for (final o in queue) {
      try {
        await repo.sendNow(o);
      } catch (_) {
        // leave in queue; will retry later
      }
    }
  }
}
