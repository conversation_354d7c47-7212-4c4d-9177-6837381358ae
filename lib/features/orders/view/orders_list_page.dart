// path: lib/features/orders/view/orders_list_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/widgets/empty_state.dart';
import '../../../core/utils/date_time.dart';
import '../providers/order_providers.dart';

class OrdersListPage extends ConsumerWidget {
  const OrdersListPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final asyncOrders = ref.watch(ordersListProvider);
    return Scaffold(
      appBar: AppBar(title: const Text('Buyurtmalar')),
      body: asyncOrders.when(
        data: (orders) => orders.isEmpty
            ? const EmptyState(message: 'Buyurtmalar yoq')
            : ListView.builder(
                itemCount: orders.length,
                itemBuilder: (c, i) {
                  final o = orders[i];
                  return ListTile(
                    leading: o.doorPhoto != null ? Image.asset(o.doorPhoto!, width: 48, height: 48, fit: BoxFit.cover) : const Icon(Icons.receipt_long),
                    title: Text(o.customerName),
                    subtitle: Text('${DateTimeUtils.formatDateTime(o.createdAt)} • ${o.phone}'),
                    trailing: Text('${o.total ~/ 1000}k'),
                    onTap: () => context.go('/orders/${o.id}'),
                  );
                },
              ),
        error: (e, _) => EmptyState(message: e.toString(), onAction: () => ref.refresh(ordersListProvider)),
        loading: () => const Center(child: CircularProgressIndicator()),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => context.go('/orders/create'),
        icon: const Icon(Icons.add),
        label: const Text('Yaratish'),
      ),
    );
  }
}
