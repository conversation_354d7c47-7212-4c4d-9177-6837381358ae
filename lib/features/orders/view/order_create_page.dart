// path: lib/features/orders/view/order_create_page.dart
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/utils/validators.dart';
import '../../../core/widgets/app_button.dart';
import '../../media/services/geolocator_service.dart';
import '../../media/services/media_picker.dart';
import '../../media/services/signature_service.dart';
import '../models/order_models.dart';
import '../providers/order_providers.dart';


class OrderCreatePage extends ConsumerStatefulWidget {
  const OrderCreatePage({super.key});
  @override
  ConsumerState<OrderCreatePage> createState() => _OrderCreatePageState();
}

class _OrderCreatePageState extends ConsumerState<OrderCreatePage> {
  final _form = GlobalKey<FormState>();
  final _name = TextEditingController();
  final _phone = TextEditingController();
  final _address = TextEditingController();
  final _qty = TextEditingController(text: '1');
  final _treeName = TextEditingController(text: 'Olma');
  final _price = TextEditingController(text: '50000');
  bool _sending = false;
  String _method = 'cash';
  String? _signaturePath;
  final List<OrderItem> _items = [];
  final List<String> _photos = [];
  String? _doorPhoto;

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(createOrderProvider);
    return Scaffold(
      appBar: AppBar(title: const Text('Buyurtma yaratish')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _form,
          child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text('Fuqaro malumotlari', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            TextFormField(controller: _name, decoration: const InputDecoration(labelText: 'F.I.Sh', border: OutlineInputBorder()), validator: Validators.requiredField),
            const SizedBox(height: 8),
            TextFormField(controller: _phone, decoration: const InputDecoration(labelText: 'Telefon', border: OutlineInputBorder()), validator: Validators.phone, keyboardType: TextInputType.phone),
            const SizedBox(height: 8),
            TextFormField(controller: _address, decoration: const InputDecoration(labelText: 'Manzil', border: OutlineInputBorder()), validator: Validators.requiredField),
            const SizedBox(height: 16),

            Text('Media & Geo', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            Wrap(spacing: 8, runSpacing: 8, children: [
              FilledButton.icon(onPressed: () async {
                final p = await MediaPicker.pickAndCompressImage();
                if (p != null) setState(()=> _photos.add(p));
              }, icon: const Icon(Icons.photo_camera), label: const Text('Kochat fotosi')),
              OutlinedButton.icon(onPressed: () async {
                final p = await MediaPicker.pickAndCompressImage();
                if (p != null) setState(()=> _doorPhoto = p);
              }, icon: const Icon(Icons.door_front_door_outlined), label: const Text('Eshik foto')),
              OutlinedButton.icon(onPressed: () async {
                final pos = await GeolocatorService.getCurrent();
                if (mounted && pos != null) {
                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Geo: ${pos.latitude}, ${pos.longitude}')));
                  state.lat = pos.latitude; state.lng = pos.longitude;
                }
              }, icon: const Icon(Icons.my_location), label: const Text('Geo olish')),
            ]),
            const SizedBox(height: 8),
            if (_photos.isNotEmpty) SizedBox(height: 80, child: ListView.separated(scrollDirection: Axis.horizontal, itemBuilder: (_,i)=>Image.asset(_photos[i], width: 80, height: 80, fit: BoxFit.cover), separatorBuilder: (_, __)=>const SizedBox(width: 8), itemCount: _photos.length)),

            const SizedBox(height: 16),
            Text('Kochatlar', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            Row(children: [
              Expanded(child: TextFormField(controller: _treeName, decoration: const InputDecoration(labelText: 'Nom (masalan: Olma)', border: OutlineInputBorder()), validator: Validators.requiredField)),
              const SizedBox(width: 8),
              SizedBox(width: 100, child: TextFormField(controller: _qty, decoration: const InputDecoration(labelText: 'Qty', border: OutlineInputBorder()), validator: Validators.qty, keyboardType: TextInputType.number)),
              const SizedBox(width: 8),
              SizedBox(width: 120, child: TextFormField(controller: _price, decoration: const InputDecoration(labelText: 'Narx', border: OutlineInputBorder()), keyboardType: TextInputType.number)),
              const SizedBox(width: 8),
              FilledButton.icon(onPressed: (){
                final q = int.tryParse(_qty.text)??1;
                final price = int.tryParse(_price.text) ?? 50000;
                setState(()=> _items.add(OrderItem(treeId: 'tree-${_items.length+1}', treeName: _treeName.text.trim(), qty: q, price: price)));
              }, icon: const Icon(Icons.add), label: const Text('Qoshish')),
            ]),
            const SizedBox(height: 8),
            if (_items.isNotEmpty)
              Column(children: _items.map((e)=>ListTile(
                title: Text('${e.treeName} x${e.qty}'),
                trailing: Text('${e.total ~/ 1000}k'),
              )).toList()),

            const SizedBox(height: 16),
            Text('Tolov', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            SegmentedButton<String>(
              segments: const [
                ButtonSegment(value:'cash', label: Text('Naqd')),
                ButtonSegment(value:'credit', label: Text('Kredit')),
              ],
              selected: {_method},
              onSelectionChanged: (s)=> setState(()=> _method = s.first),
            ),
            const SizedBox(height: 8),
            if (_method == 'cash' || _method == 'credit') OutlinedButton.icon(
              onPressed: () async {
                final p = await SignatureService.captureSignature(context);
                if (p != null) setState(()=> _signaturePath = p);
              }, icon: const Icon(Icons.gesture), label: Text(_signaturePath==null ? 'Imzo olish' : 'Imzo olingan'),
            ),

            const SizedBox(height: 24),
            Align(
              alignment: Alignment.centerRight,
              child: AppButton(label: _sending ? '...' : 'Saqlash (offline queue)', loading: _sending, onPressed: _onSubmit),
            ),
            const SizedBox(height: 24),
          ]),
        ),
      ),
    );
  }

  Future<void> _onSubmit() async {
    if (!_form.currentState!.validate()) return;
    setState(()=> _sending = true);
    try {
      final id = 'L${DateTime.now().millisecondsSinceEpoch}-${Random().nextInt(999)}';
      final order = OrderModel(
        id: id,
        createdAt: DateTime.now(),
        customerName: _name.text.trim(),
        phone: _phone.text.trim(),
        address: _address.text.trim(),
        lat: ref.read(createOrderProvider).lat,
        lng: ref.read(createOrderProvider).lng,
        photos: _photos,
        doorPhoto: _doorPhoto,
        signaturePath: _signaturePath,
        paymentMethod: _method,
        items: _items.isNotEmpty ? _items : [OrderItem(treeId: 't1', treeName: 'Olma', qty: 1, price: 50000)],
        status: 'pending',
      );
      final repo = ref.read(ordersRepoProvider);
      await repo.queueCreate(order);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Saqlandi, internet bolganda yuboriladi')));
        context.go('/orders');
      }
    } catch (e) {
      if (mounted) ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(e.toString())));
    } finally {
      if (mounted) setState(()=> _sending = false);
    }
  }
}
