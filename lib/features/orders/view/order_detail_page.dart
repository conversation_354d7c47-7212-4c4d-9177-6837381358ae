// path: lib/features/orders/view/order_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../core/widgets/empty_state.dart';
import '../../../core/utils/date_time.dart';
import '../providers/order_providers.dart';

class OrderDetailPage extends ConsumerWidget {
  final String orderId;
  const OrderDetailPage({super.key, required this.orderId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final asyncOrder = ref.watch(orderByIdProvider(orderId));
    return Scaffold(
      appBar: AppBar(title: const Text('Buyurtma')),
      body: asyncOrder.when(
        data: (o) => ListView(
          padding: const EdgeInsets.all(16),
          children: [
            ListTile(title: Text(o.customerName), subtitle: Text(o.phone)),
            ListTile(title: const Text('Sana'), subtitle: Text(DateTimeUtils.formatDateTime(o.createdAt))),
            ListTile(title: const Text('Manzil'), subtitle: Text(o.address)),
            ListTile(title: const Text('Tolov turi'), subtitle: Text(o.paymentMethod)),
            ListTile(title: const Text('Jami'), trailing: Text('${o.total} som')),
            const Divider(),
            const Text('Tovarlar', style: TextStyle(fontWeight: FontWeight.bold)),
            ...o.items.map((e)=>ListTile(
              dense: true,
              title: Text('${e.treeName} x${e.qty}'),
              trailing: Text('${e.total}'),
            )),
            const SizedBox(height: 12),
            if (o.contractUrl != null)
              FilledButton.icon(
                onPressed: () async { final uri = Uri.parse(o.contractUrl!); if (await canLaunchUrl(uri)) await launchUrl(uri, mode: LaunchMode.externalApplication); },
                icon: const Icon(Icons.picture_as_pdf_outlined),
                label: const Text('PDF shartnoma'),
              ),
          ],
        ),
        error: (e, _) => EmptyState(message: e.toString()),
        loading: () => const Center(child: CircularProgressIndicator()),
      ),
    );
  }
}
