// path: lib/features/orders/models/order_models.dart
class OrderItem {
  final String treeId;
  final String treeName;
  final int qty;
  final int price; // so'm
  OrderItem({required this.treeId, required this.treeName, required this.qty, required this.price});
  int get total => qty * price;

  Map<String, dynamic> toJson() => {'treeId': treeId, 'treeName': treeName, 'qty': qty, 'price': price};
  factory OrderItem.fromJson(Map<String, dynamic> j) => OrderItem(treeId: j['treeId'], treeName: j['treeName'] ?? '', qty: j['qty'] ?? 1, price: j['price'] ?? 0);
}

class OrderModel {
  final String id;
  final DateTime createdAt;
  final String customerName;
  final String phone;
  final String address;
  final double? lat;
  final double? lng;
  final List<String> photos;     // local file paths or remote urls
  final String? doorPhoto;
  final String? signaturePath;
  final String paymentMethod;    // cash | credit
  final List<OrderItem> items;
  final String status;           // pending|sent|completed
  final String? contractUrl;

  OrderModel({
    required this.id,
    required this.createdAt,
    required this.customerName,
    required this.phone,
    required this.address,
    this.lat,
    this.lng,
    this.photos = const [],
    this.doorPhoto,
    this.signaturePath,
    required this.paymentMethod,
    required this.items,
    this.status = 'pending',
    this.contractUrl,
  });

  int get total => items.fold(0, (s, e) => s + e.total);

  Map<String, dynamic> toJson() => {
        'id': id,
        'createdAt': createdAt.toIso8601String(),
        'customerName': customerName,
        'phone': phone,
        'address': address,
        'lat': lat,
        'lng': lng,
        'photos': photos,
        'doorPhoto': doorPhoto,
        'signaturePath': signaturePath,
        'paymentMethod': paymentMethod,
        'items': items.map((e) => e.toJson()).toList(),
        'status': status,
        'contractUrl': contractUrl,
      };

  factory OrderModel.fromJson(Map<String, dynamic> j) => OrderModel(
        id: j['id'],
        createdAt: DateTime.tryParse(j['createdAt'] ?? '') ?? DateTime.now(),
        customerName: j['customerName'] ?? '',
        phone: j['phone'] ?? '',
        address: j['address'] ?? '',
        lat: (j['lat'] as num?)?.toDouble(),
        lng: (j['lng'] as num?)?.toDouble(),
        photos: (j['photos'] as List?)?.cast<String>() ?? const [],
        doorPhoto: j['doorPhoto'],
        signaturePath: j['signaturePath'],
        paymentMethod: j['paymentMethod'] ?? 'cash',
        items: ((j['items'] as List?) ?? []).map((e) => OrderItem.fromJson(Map<String, dynamic>.from(e))).toList(),
        status: j['status'] ?? 'pending',
        contractUrl: j['contractUrl'],
      );
}
