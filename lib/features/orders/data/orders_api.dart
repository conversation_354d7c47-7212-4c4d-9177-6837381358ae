// path: lib/features/orders/data/orders_api.dart
import 'package:dio/dio.dart';
import '../../../core/env/env.dart';
import '../../../core/error/app_exception.dart';
import '../models/order_models.dart';

class OrdersApi {
  final Dio dio;
  OrdersApi(this.dio);

  Future<List<OrderModel>> getMyOrders({int page = 1, int limit = 20, DateTime? from, DateTime? to}) async {
    if (Env.useMock) {
      // Simple mock
      await Future.delayed(const Duration(milliseconds: 400));
      return List.generate(3, (i) => OrderModel(
        id: 'MOCK-${DateTime.now().millisecondsSinceEpoch}-$i',
        createdAt: DateTime.now().subtract(Duration(days: i)),
        customerName: 'Fuqaro $i',
        phone: '+99890000000$i',
        address: 'Toshkent, kocha $i',
        paymentMethod: 'cash',
        items: [OrderItem(treeId: 't$i', treeName: 'Olma ($i)', qty: 1+i, price: 50000)],
      ));
    }
    try {
      final res = await dio.get('/orders', queryParameters: {
        'mine': true,
        'page': page,
        'limit': limit,
        if (from != null) 'from': from.toIso8601String(),
        if (to != null) 'to': to.toIso8601String(),
      });
      final list = (res.data as List).map((e) => OrderModel.fromJson(Map<String, dynamic>.from(e))).toList();
      return list;
    } on DioException catch (e) {
      throw AppException(e.message ?? 'Orders yuklash xatosi', statusCode: e.response?.statusCode);
    }
  }

  Future<OrderModel> createOrder(OrderModel order) async {
    if (Env.useMock) {
      await Future.delayed(const Duration(milliseconds: 300));
      return order; // mock: echo back
    }
    final form = FormData.fromMap({
      'customer': {
        'name': order.customerName,
        'phone': order.phone,
        'address': order.address,
      },
      'media': {
        'lat': order.lat,
        'lng': order.lng,
      },
      'payment': {
        'method': order.paymentMethod,
      },
      'items': order.items.map((e) => e.toJson()).toList(),
      if (order.signaturePath != null) 'signature': await MultipartFile.fromFile(order.signaturePath!, filename: 'signature.png'),
      for (int i = 0; i < order.photos.length; i++)
        'photo$i': await MultipartFile.fromFile(order.photos[i], filename: 'photo_$i.jpg'),
    });
    try {
      final res = await dio.post('/orders', data: form);
      return OrderModel.fromJson(Map<String, dynamic>.from(res.data));
    } on DioException catch (e) {
      throw AppException(e.response?.data?['message']?.toString() ?? 'Yaratish xatosi', statusCode: e.response?.statusCode);
    }
  }

  Future<OrderModel> getById(String id) async {
    if (Env.useMock) {
      await Future.delayed(const Duration(milliseconds: 200));
      return OrderModel(
        id: id,
        createdAt: DateTime.now(),
        customerName: 'Demo',
        phone: '+99890XXXXXXX',
        address: 'Demo manzil',
        paymentMethod: 'cash',
        items: [OrderItem(treeId: 't1', treeName: 'Olma', qty: 2, price: 50000)],
        status: 'sent',
        contractUrl: 'https://example.com/contract.pdf',
      );
    }
    try {
      final res = await dio.get('/orders/$id');
      return OrderModel.fromJson(Map<String, dynamic>.from(res.data));
    } on DioException catch (e) {
      throw AppException(e.message ?? 'Order topilmadi', statusCode: e.response?.statusCode);
    }
  }
}
