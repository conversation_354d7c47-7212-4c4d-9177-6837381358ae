// path: lib/features/orders/data/orders_local.dart
import 'package:hive_flutter/hive_flutter.dart';
import '../models/order_models.dart';

class OrdersLocal {
  static const _boxOrders = 'orders_cache';
  static const _boxQueue = 'orders_queue';

  Future<void> init() async {
    await Hive.initFlutter();
    await Hive.openBox(_boxOrders);
    await Hive.openBox(_boxQueue);
  }

  Future<List<OrderModel>> getCached() async {
    final box = Hive.box(_boxOrders);
    final list = (box.values.cast<Map>().toList()).map((e) => OrderModel.fromJson(Map<String, dynamic>.from(e))).toList();
    list.sort((a,b)=>b.createdAt.compareTo(a.createdAt));
    return list;
  }

  Future<void> saveCache(List<OrderModel> orders) async {
    final box = Hive.box(_boxOrders);
    await box.clear();
    for (final o in orders) {
      await box.put(o.id, o.toJson());
    }
  }

  Future<void> upsertOrder(OrderModel o) async {
    final box = Hive.box(_boxOrders);
    await box.put(o.id, o.toJson());
  }

  Future<void> enqueue(OrderModel o) async {
    final box = Hive.box(_boxQueue);
    await box.put(o.id, o.toJson());
  }

  Future<List<OrderModel>> getQueue() async {
    final box = Hive.box(_boxQueue);
    return box.values.map((e) => OrderModel.fromJson(Map<String, dynamic>.from(e))).toList();
  }

  Future<void> dequeue(String id) async {
    final box = Hive.box(_boxQueue);
    await box.delete(id);
  }
}
