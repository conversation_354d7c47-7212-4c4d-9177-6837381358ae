// path: lib/features/orders/repo/orders_repo.dart
import '../../../core/network/dio_client.dart';
import '../../auth/data/auth_local.dart';
import '../data/orders_api.dart';
import '../data/orders_local.dart';
import '../models/order_models.dart';

class OrdersRepo {
  final OrdersApi api;
  final OrdersLocal local;

  OrdersRepo._(this.api, this.local);

  static Future<OrdersRepo> create(AuthLocal auth, OrdersLocal local, Future<bool> Function() tryRefresh) async {
    final dio = DioClient.build(
      getAccessToken: () => auth.getAccess(),
      getRefreshToken: () => auth.getRefresh(),
      tryRefresh: tryRefresh,
    );
    await local.init();
    return OrdersRepo._(OrdersApi(dio), local);
  }

  Future<List<OrderModel>> list({int page = 1}) async {
    final cached = await local.getCached();
    try {
      final remote = await api.getMyOrders(page: page);
      await local.saveCache(remote);
      return remote;
    } catch (_) {
      return cached;
    }
  }

  Future<void> queueCreate(OrderModel o) async {
    await local.enqueue(o);
    await local.upsertOrder(o);
  }

  Future<OrderModel> getById(String id) async {
    try {
      final r = await api.getById(id);
      await local.upsertOrder(r);
      return r;
    } catch (_) {
      final cached = (await local.getCached()).where((e) => e.id == id).toList();
      if (cached.isNotEmpty) return cached.first;
      rethrow;
    }
  }

  Future<OrderModel> sendNow(OrderModel o) async {
    final sent = await api.createOrder(o);
    await local.upsertOrder(sent);
    await local.dequeue(o.id);
    return sent;
  }

  Future<List<OrderModel>> getQueue() => local.getQueue();
  Future<void> dequeue(String id) => local.dequeue(id);
}
