// path: lib/features/orders/providers/order_providers.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../auth/providers/auth_providers.dart';
import '../data/orders_local.dart';
import '../models/order_models.dart';
import '../repo/orders_repo.dart';

final ordersLocalProvider = Provider<OrdersLocal>((ref) => OrdersLocal());

final ordersRepoProvider = Provider<OrdersRepo>((ref) {
  throw UnimplementedError('Initialized in ordersInitProvider');
});

final ordersInitProvider = FutureProvider<void>((ref) async {
  await ref.read(authInitProvider.future); // ensure auth repo exists
  final authRepo = ref.read(authRepoProvider);
  final local = ref.read(ordersLocalProvider);
  final repo = await OrdersRepo.create(ref.read(authLocalProvider), local, () async => await authRepo.refresh());
  // ignore: invalid_use_of_internal_member
  ref.container.updateOverrides([ordersRepoProvider.overrideWithValue(repo)]);
});

final ordersListProvider = FutureProvider<List<OrderModel>>((ref) async {
  await ref.read(ordersInitProvider.future);
  final repo = ref.read(ordersRepoProvider);
  return repo.list();
});

final orderByIdProvider = FutureProvider.family<OrderModel, String>((ref, id) async {
  await ref.read(ordersInitProvider.future);
  final repo = ref.read(ordersRepoProvider);
  return repo.getById(id);
});

class CreateOrderState {
  String customerName = '';
  String phone = '';
  String address = '';
  double? lat;
  double? lng;
  List<String> photos = [];
  String? doorPhoto;
  String paymentMethod = 'cash';
  String? signaturePath;
  final List<OrderItem> items = [];
}

final createOrderProvider = StateProvider<CreateOrderState>((ref) => CreateOrderState());
