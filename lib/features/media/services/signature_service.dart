// path: lib/features/media/services/signature_service.dart
import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:signature/signature.dart';
import 'package:path_provider/path_provider.dart';

class SignatureService {
  static Future<String?> captureSignature(BuildContext context) async {
    final sigController = SignatureController(penStrokeWidth: 3, penColor: Colors.black);
    final path = await Navigator.of(context).push<String>(MaterialPageRoute(builder: (_) => _SignaturePage(controller: sigController)));
    return path;
  }
}

class _SignaturePage extends StatefulWidget {
  final SignatureController controller;
  const _SignaturePage({required this.controller});
  @override
  State<_SignaturePage> createState() => _SignaturePageState();
}

class _SignaturePageState extends State<_SignaturePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Imzo')),
      body: Column(children: [
        Expanded(child: Container(color: Colors.white, child: Signature(controller: widget.controller, backgroundColor: Colors.white))),
        Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
          TextButton(onPressed: ()=> widget.controller.clear(), child: const Text('Tozalash')),
          FilledButton(onPressed: _save, child: const Text('Saqlash')),
        ]),
        const SizedBox(height: 12),
      ]),
    );
  }

  Future<void> _save() async {
    final data = await widget.controller.toImage();
    if (data == null) return;
    final byteData = await data.toByteData(format: ui.ImageByteFormat.png);
    if (byteData == null) return;
    final dir = await getApplicationDocumentsDirectory();
    final file = File('${dir.path}/sig_${DateTime.now().millisecondsSinceEpoch}.png');
    await file.writeAsBytes(byteData.buffer.asUint8List());
    if (mounted) Navigator.pop(context, file.path);
  }
}
