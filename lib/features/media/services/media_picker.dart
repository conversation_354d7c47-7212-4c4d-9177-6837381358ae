// path: lib/features/media/services/media_picker.dart
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';

class MediaPicker {
  static Future<String?> pickAndCompressImage({ImageSource source = ImageSource.camera}) async {
    final picker = ImagePicker();
    final x = await picker.pickImage(source: source, imageQuality: 100);
    if (x == null) return null;
    final dir = await getApplicationDocumentsDirectory();
    final outPath = '${dir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
    final result = await FlutterImageCompress.compressAndGetFile(x.path, outPath, quality: 85, minWidth: 1080, minHeight: 1080);
    return result?.path ?? x.path;
  }
}
