// path: lib/features/media/services/geolocator_service.dart
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

class GeolocatorService {
  static Future<Position?> getCurrent() async {
    final p = await Permission.locationWhenInUse.request();
    if (!p.isGranted) return null;
    final enabled = await Geolocator.isLocationServiceEnabled();
    if (!enabled) return null;
    return Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
  }
}
