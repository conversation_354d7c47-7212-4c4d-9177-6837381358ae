// path: lib/features/auth/view/login_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/widgets/app_button.dart';
import '../../../core/widgets/app_text_field.dart';
import '../../../core/utils/validators.dart';
import '../../../core/env/env.dart';
import '../providers/auth_providers.dart';

class LoginPage extends ConsumerStatefulWidget {
  final Object flavor;
  const LoginPage({super.key, required this.flavor});

  @override
  ConsumerState<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<LoginPage> {
  final _form = GlobalKey<FormState>();
  final _u = TextEditingController();
  final _p = TextEditingController();
  bool _loading = false;
  String? _error;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(Env.appName)),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _form,
          child: Column(
            children: [
              AppTextField(controller: _u, label: 'Login', validator: Validators.requiredField),
              const SizedBox(height: 12),
              AppTextField(controller: _p, label: 'Parol', obscure: true, validator: Validators.requiredField),
              const SizedBox(height: 12),
              if (_error != null) Text(_error!, style: TextStyle(color: Theme.of(context).colorScheme.error)),
              const Spacer(),
              AppButton(
                label: _loading ? '...' : 'Kirish',
                loading: _loading,
                onPressed: () async {
                  if (!_form.currentState!.validate()) return;
                  setState(() => _loading = true);
                  final repo = ref.read(authRepoProvider);
                  try {
                    final (mustChange, error) = await repo.login(_u.text.trim(), _p.text.trim());
                    if (error != null) throw Exception(error);
                    if (mustChange) {
                      if (mounted) context.go('/change-password');
                    } else {
                      if (mounted) context.go('/orders');
                    }
                  } catch (e) {
                    setState(() => _error = e.toString());
                  } finally {
                    if (mounted) setState(() => _loading = false);
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
