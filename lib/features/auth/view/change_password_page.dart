// path: lib/features/auth/view/change_password_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/widgets/app_button.dart';
import '../../../core/widgets/app_text_field.dart';
import '../../../core/utils/validators.dart';
import '../providers/auth_providers.dart';

class ChangePasswordPage extends ConsumerStatefulWidget {
  const ChangePasswordPage({super.key});
  @override
  ConsumerState<ChangePasswordPage> createState() => _ChangePasswordPageState();
}

class _ChangePasswordPageState extends ConsumerState<ChangePasswordPage> {
  final _form = GlobalKey<FormState>();
  final _old = TextEditingController();
  final _new = TextEditingController();
  bool _loading = false;
  String? _msg;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Parolni almashtirish')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _form,
          child: Column(children: [
            AppTextField(controller: _old, label: 'Eski parol', obscure: true, validator: Validators.requiredField),
            const SizedBox(height: 12),
            AppTextField(controller: _new, label: 'Yangi parol', obscure: true, validator: Validators.requiredField),
            const Spacer(),
            if (_msg != null) Text(_msg!),
            AppButton(
              label: _loading ? '...' : 'Saqlash',
              loading: _loading,
              onPressed: () async {
                if (!_form.currentState!.validate()) return;
                setState(() => _loading = true);
                try {
                  await ref.read(authRepoProvider).changePassword(_old.text, _new.text);
                  if (mounted) setState(() => _msg = 'Parol yangilandi');
                } catch (e) {
                  if (mounted) setState(() => _msg = e.toString());
                } finally {
                  if (mounted) setState(() => _loading = false);
                }
              },
            ),
          ]),
        ),
      ),
    );
  }
}
