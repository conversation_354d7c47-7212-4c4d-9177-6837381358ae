// path: lib/features/auth/providers/auth_providers.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../data/auth_local.dart';
import '../repo/auth_repo.dart';

final authLocalProvider = Provider<AuthLocal>((ref) => AuthLocal());

final tokensProvider = StateProvider<(String? access, String? refresh)>((ref) => (null, null));

final authRepoProvider = Provider<AuthRepo>((ref) {
  throw UnimplementedError('Initialized in authInitProvider');
});

final authInitProvider = FutureProvider<void>((ref) async {
  final local = ref.read(authLocalProvider);
  Future<String?> getAccess() async => (await local.getAccess());
  Future<String?> getRefresh() async => (await local.getRefresh());
  late AuthRepo repo;
  repo = await AuthRepo.create(local, () async => await repo.refresh(), getAccess, getRefresh);
  ref.onDispose(() {});
  ref.keepAlive();
  ref.state = ref.state; // no-op
  // Hack to put repo in container:
  // ignore: invalid_use_of_internal_member
  ref.container.updateOverrides([authRepoProvider.overrideWithValue(repo)]);
});
