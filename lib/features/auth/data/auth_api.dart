// path: lib/features/auth/data/auth_api.dart
import 'package:dio/dio.dart';
import '../../../core/error/app_exception.dart';

class AuthApi {
  final Dio dio;
  AuthApi(this.dio);

  Future<(String access, String refresh, bool mustChange)> login(String username, String password) async {
    try {
      final res = await dio.post('/auth/login', data: {'username': username, 'password': password});
      final d = res.data as Map;
      return (d['accessToken'] as String, d['refreshToken'] as String, (d['mustChangePassword'] ?? false) as bool);
    } on DioException catch (e) {
      throw AppException(e.response?.data?['message']?.toString() ?? 'Login xatosi', statusCode: e.response?.statusCode);
    }
  }

  Future<(String access, String refresh)> refresh(String refreshToken) async {
    try {
      final res = await dio.post('/auth/refresh', data: {'refreshToken': refreshToken});
      final d = res.data as Map;
      return (d['accessToken'] as String, d['refreshToken'] as String);
    } on DioException catch (_) {
      throw AppException.auth('Refresh xatosi');
    }
  }

  Future<void> changePassword(String oldPwd, String newPwd) async {
    try {
      await dio.post('/auth/change-password', data: {'oldPassword': oldPwd, 'newPassword': newPwd});
    } on DioException catch (e) {
      throw AppException(e.response?.data?['message']?.toString() ?? 'Parol almashtirish xatosi');
    }
  }
}
