// path: lib/features/auth/data/auth_local.dart
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class AuthLocal {
  final FlutterSecureStorage _sec = const FlutterSecureStorage();
  static const _kAccess = 'access_token';
  static const _kRefresh = 'refresh_token';

  Future<void> saveTokens(String access, String refresh) async {
    await _sec.write(key: _kAccess, value: access);
    await _sec.write(key: _kRefresh, value: refresh);
  }

  Future<String?> getAccess() async => _sec.read(key: _kAccess);
  Future<String?> getRefresh() async => _sec.read(key: _kRefresh);

  Future<void> clear() async {
    await _sec.deleteAll();
  }
}
