// path: lib/features/auth/repo/auth_repo.dart
import 'package:dio/dio.dart';
import '../../../core/env/env.dart';
import '../../../core/network/dio_client.dart';
import '../data/auth_api.dart';
import '../data/auth_local.dart';

class AuthRepo {
  final AuthLocal local;
  late final AuthApi api;

  AuthRepo._(this.local, Dio dio) {
    api = AuthApi(dio);
  }

  static Future<AuthRepo> create(AuthLocal local, Future<bool> Function() tryRefresh, Future<String?> Function() getAccess, Future<String?> Function() getRefresh) async {
    final dio = DioClient.build(getAccessToken: getAccess, getRefreshToken: getRefresh, tryRefresh: tryRefresh);
    return AuthRepo._(local, dio);
  }

  Future<(bool mustChange, String? error)> login(String u, String p) async {
    if (Env.useMock) {
      await local.saveTokens('mock_access', 'mock_refresh');
      return (false, null);
    }
    try {
      final (access, refresh, mustChange) = await api.login(u, p);
      await local.saveTokens(access, refresh);
      return (mustChange, null);
    } catch (e) {
      return (false, e.toString());
    }
  }

  Future<bool> refresh() async {
    if (Env.useMock) {
      await local.saveTokens('mock_access', 'mock_refresh');
      return true;
    }
    final r = await local.getRefresh();
    if (r == null) return false;
    try {
      final (a, n) = await api.refresh(r);
      await local.saveTokens(a, n);
      return true;
    } catch (_) {
      await logout();
      return false;
    }
  }

  Future<void> logout() async {
    await local.clear();
  }

  Future<void> changePassword(String oldP, String newP) async {
    if (Env.useMock) return;
    await api.changePassword(oldP, newP);
  }
}
