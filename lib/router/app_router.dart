// path: lib/router/app_router.dart
import 'package:go_router/go_router.dart';
import '../app.dart';
import '../features/auth/view/login_page.dart';
import '../features/auth/view/change_password_page.dart';
import '../features/orders/view/orders_list_page.dart';
import '../features/orders/view/order_create_page.dart';
import '../features/orders/view/order_detail_page.dart';

GoRouter createRouter(AppFlavor flavor) {
  return GoRouter(
    initialLocation: '/login',
    routes: [
      GoRoute(
        path: '/login',
        builder: (c, s) => LoginPage(flavor: flavor),
      ),
      GoRoute(
        path: '/change-password',
        builder: (c, s) => const ChangePasswordPage(),
      ),
      GoRoute(
        path: '/orders',
        builder: (c, s) => const OrdersListPage(),
      ),
      GoRoute(
        path: '/orders/create',
        builder: (c, s) => const OrderCreatePage(),
      ),
      GoRoute(
        path: '/orders/:id',
        builder: (c, s) => OrderDetailPage(orderId: s.pathParameters['id']!),
      ),
    ],
  );
}
