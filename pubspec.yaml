# path: pubspec.yaml
name: mobil_kochat_worker
description: Mobil <PERSON>'chat - Worker mobile app (offline-first)
publish_to: "none"
version: 1.0.0+1

environment:
  sdk: ">=3.4.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State
  flutter_riverpod: ^2.5.1

  # Network
  dio: ^5.6.0
  pretty_dio_logger: ^1.3.1

  # Routing
  go_router: ^14.2.0

  # Env
  flutter_dotenv: ^5.1.0

  # Storage & cache
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.5
  flutter_secure_storage: ^9.2.2

  # Permissions & device
  permission_handler: ^11.3.1
  geolocator: ^12.0.0
  connectivity_plus: ^6.0.5

  # Media
  image_picker: ^1.1.2
  flutter_image_compress: ^2.3.0
  signature: ^5.5.0

  # Open links/PDF
  url_launcher: ^6.3.0
  intl: ^0.20.2

  # UI helpers
  collection: ^1.18.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

flutter:
  uses-material-design: true
  assets:
    - .env
