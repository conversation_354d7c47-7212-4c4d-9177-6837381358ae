Zo‘r — mana “<PERSON><PERSON> Ko‘chat” uchun Worker mobil il<PERSON> (Flutter 3 / Dart 3, Riverpod, Dio, Hive, SecureStorage, go\_router, dotenv va h.k.). Kod ko‘p faylli, har birining tepasida `// path:` bor. Ilova **offline-first**: buyurtmalar navbatga (Hive) tushadi, internet bo‘lganda SyncService jo‘natadi. Dev flavor mock API’ni qo‘llaydi (backend yo‘q bo‘lsa ham demo ishlaydi); Prod esa real endpointlarga ulanadi.

Quyida **asosiy fayllar** berilgan: `pubspec.yaml`, env, entrypoints, yadro/core, autentifikatsiya, orders oqimi (ro‘yxat, yaratish, detal), media/geolokatsiya/signature servislar, sync xizmati, UI vidjetlar va router. Qolgan kichik util-fayllar ham kiritilgan. Bularni nomi bilan papkalarga joylashtiring.

---

### `pubspec.yaml`

```yaml
# path: pubspec.yaml
name: mobil_kochat_worker
description: Mobil Ko'chat - Worker mobile app (offline-first)
publish_to: "none"
version: 1.0.0+1

environment:
  sdk: ">=3.4.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State
  flutter_riverpod: ^2.5.1

  # Network
  dio: ^5.6.0
  pretty_dio_logger: ^1.3.1

  # Routing
  go_router: ^14.2.0

  # Env
  flutter_dotenv: ^5.1.0

  # Storage & cache
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.5
  flutter_secure_storage: ^9.2.2

  # Permissions & device
  permission_handler: ^11.3.1
  geolocator: ^12.0.0
  connectivity_plus: ^6.0.5

  # Media
  image_picker: ^1.1.2
  flutter_image_compress: ^2.3.0
  signature: ^5.5.0

  # Open links/PDF
  url_launcher: ^6.3.0
  intl: ^0.19.0

  # UI helpers
  collection: ^1.18.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

flutter:
  uses-material-design: true
  assets:
    - .env
```

---

### `.env` (dev uchun namunaviy)

```dotenv
# path: .env
API_BASE_URL=http://10.0.2.2:4000
APP_NAME=Mobil Ko'chat Worker (DEV)
USE_MOCK=true
```

> Prod’da `USE_MOCK=false` qilib, `.env.prod` yaratishingiz mumkin.

---

## Entry points, App, Router, Theme

```dart
// path: lib/main_dev.dart
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'app.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: ".env");
  runApp(const ProviderScope(child: MobilKochatApp(flavor: AppFlavor.dev)));
}
```

```dart
// path: lib/main_prod.dart
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'app.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: ".env.prod");
  runApp(const ProviderScope(child: MobilKochatApp(flavor: AppFlavor.prod)));
}
```

```dart
// path: lib/app.dart
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'router/app_router.dart';
import 'theme/app_theme.dart';

enum AppFlavor { dev, prod }

class MobilKochatApp extends StatelessWidget {
  final AppFlavor flavor;
  const MobilKochatApp({super.key, required this.flavor});

  @override
  Widget build(BuildContext context) {
    final router = createRouter(flavor);
    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      title: "Mobil Ko'chat Worker",
      theme: AppTheme.light,
      darkTheme: AppTheme.dark,
      routerConfig: router,
      locale: const Locale('uz'),
      supportedLocales: const [Locale('uz'), Locale('ru')],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ],
    );
  }
}
```

```dart
// path: lib/router/app_router.dart
import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import '../app.dart';
import '../features/auth/view/login_page.dart';
import '../features/auth/view/change_password_page.dart';
import '../features/orders/view/orders_list_page.dart';
import '../features/orders/view/order_create_page.dart';
import '../features/orders/view/order_detail_page.dart';

GoRouter createRouter(AppFlavor flavor) {
  return GoRouter(
    initialLocation: '/login',
    routes: [
      GoRoute(
        path: '/login',
        builder: (c, s) => LoginPage(flavor: flavor),
      ),
      GoRoute(
        path: '/change-password',
        builder: (c, s) => const ChangePasswordPage(),
      ),
      GoRoute(
        path: '/orders',
        builder: (c, s) => const OrdersListPage(),
      ),
      GoRoute(
        path: '/orders/create',
        builder: (c, s) => const OrderCreatePage(),
      ),
      GoRoute(
        path: '/orders/:id',
        builder: (c, s) => OrderDetailPage(orderId: s.pathParameters['id']!),
      ),
    ],
  );
}
```

```dart
// path: lib/theme/app_theme.dart
import 'package:flutter/material.dart';

class AppTheme {
  static ThemeData get light => ThemeData(
        useMaterial3: true,
        colorSchemeSeed: const Color(0xFF2E7D32),
        brightness: Brightness.light,
      );

  static ThemeData get dark => ThemeData(
        useMaterial3: true,
        colorSchemeSeed: const Color(0xFF66BB6A),
        brightness: Brightness.dark,
      );
}
```

---

## Core: Env, Network, Errors, Utils, Widgets

```dart
// path: lib/core/env/env.dart
import 'package:flutter_dotenv/flutter_dotenv.dart';

class Env {
  static String get apiBaseUrl => dotenv.env['API_BASE_URL'] ?? 'http://10.0.2.2:4000';
  static String get appName => dotenv.env['APP_NAME'] ?? "Mobil Ko'chat Worker";
  static bool get useMock => (dotenv.env['USE_MOCK'] ?? 'false').toLowerCase() == 'true';
}
```

```dart
// path: lib/core/error/app_exception.dart
class AppException implements Exception {
  final String message;
  final int? statusCode;
  AppException(this.message, {this.statusCode});
  @override
  String toString() => 'AppException($statusCode): $message';

  static AppException network([String m = 'Network error']) => AppException(m);
  static AppException auth([String m = 'Auth error']) => AppException(m, statusCode: 401);
  static AppException server([String m = 'Server error']) => AppException(m, statusCode: 500);
  static AppException validation([String m = 'Validation error']) => AppException(m, statusCode: 422);
  static AppException offline([String m = 'Offline']) => AppException(m);
}
```

```dart
// path: lib/core/network/dio_client.dart
import 'package:dio/dio.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import '../env/env.dart';
import 'interceptors/auth_interceptor.dart';
import 'interceptors/retry_interceptor.dart';

class DioClient {
  static Dio build({
    required Future<String?> Function() getAccessToken,
    required Future<String?> Function() getRefreshToken,
    required Future<bool> Function() tryRefresh,
  }) {
    final dio = Dio(BaseOptions(baseUrl: Env.apiBaseUrl, connectTimeout: const Duration(seconds: 15), receiveTimeout: const Duration(seconds: 30)));
    dio.interceptors.add(AuthInterceptor(getAccessToken: getAccessToken, tryRefresh: tryRefresh));
    dio.interceptors.add(RetryInterceptor(dio));
    dio.interceptors.add(PrettyDioLogger(requestHeader: true, requestBody: false, responseBody: false));
    return dio;
  }
}
```

```dart
// path: lib/core/network/interceptors/auth_interceptor.dart
import 'package:dio/dio.dart';

class AuthInterceptor extends Interceptor {
  final Future<String?> Function() getAccessToken;
  final Future<bool> Function() tryRefresh;
  AuthInterceptor({required this.getAccessToken, required this.tryRefresh});

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final token = await getAccessToken();
    if (token != null && token.isNotEmpty) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      final refreshed = await tryRefresh();
      if (refreshed) {
        final req = err.requestOptions;
        final clone = await _retryRequest(req);
        return handler.resolve(clone);
      }
    }
    handler.next(err);
  }

  Future<Response<dynamic>> _retryRequest(RequestOptions req) async {
    final dio = Dio()..options = req;
    return dio.fetch(req);
  }
}
```

```dart
// path: lib/core/network/interceptors/retry_interceptor.dart
import 'dart:math';
import 'package:dio/dio.dart';

class RetryInterceptor extends Interceptor {
  final Dio dio;
  RetryInterceptor(this.dio);

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final shouldRetry = err.type != DioExceptionType.cancel &&
        (err.type == DioExceptionType.connectionTimeout ||
         err.type == DioExceptionType.unknown ||
         (err.response?.statusCode ?? 0) >= 500);

    if (!shouldRetry) return handler.next(err);

    int attempt = (err.requestOptions.extra['__retry_attempt'] ?? 0) + 1;
    if (attempt > 3) return handler.next(err);

    final delayMs = min(2000 * pow(2, attempt - 1).toInt(), 8000);
    await Future.delayed(Duration(milliseconds: delayMs));
    final opts = err.requestOptions..extra['__retry_attempt'] = attempt;
    try {
      final res = await dio.fetch(opts);
      return handler.resolve(res);
    } catch (e) {
      return handler.next(err);
    }
  }
}
```

```dart
// path: lib/core/utils/date_time.dart
import 'package:intl/intl.dart';

class DateTimeUtils {
  static String formatDate(DateTime d) => DateFormat('yyyy-MM-dd').format(d);
  static String formatDateTime(DateTime d) => DateFormat('yyyy-MM-dd HH:mm').format(d);
}
```

```dart
// path: lib/core/utils/validators.dart
class Validators {
  static String? requiredField(String? v) => (v == null || v.trim().isEmpty) ? 'Majburiy' : null;
  static String? phone(String? v) {
    if (v == null || v.trim().isEmpty) return 'Telefon kiritilsin';
    final p = RegExp(r'^\+?\d{9,15}$');
    return p.hasMatch(v) ? null : 'Telefon noto‘g‘ri';
  }
  static String? passport(String? v) {
    if (v == null || v.trim().isEmpty) return null;
    final p = RegExp(r'^[A-Z]{2}\d{7}$');
    return p.hasMatch(v) ? null : 'Passport noto‘g‘ri (*********)';
  }
  static String? qty(String? v) {
    final n = int.tryParse(v ?? '');
    if (n == null || n < 1) return 'Qty ≥ 1';
    return null;
  }
}
```

```dart
// path: lib/core/widgets/app_button.dart
import 'package:flutter/material.dart';

class AppButton extends StatelessWidget {
  final String label;
  final VoidCallback? onPressed;
  final bool loading;
  const AppButton({super.key, required this.label, this.onPressed, this.loading = false});
  @override
  Widget build(BuildContext context) {
    return FilledButton(
      onPressed: loading ? null : onPressed,
      child: loading
          ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(strokeWidth: 2))
          : Text(label),
    );
  }
}
```

```dart
// path: lib/core/widgets/app_text_field.dart
import 'package:flutter/material.dart';

class AppTextField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final TextInputType? keyboardType;
  final String? Function(String?)? validator;
  final bool obscure;
  const AppTextField({super.key, required this.controller, required this.label, this.keyboardType, this.validator, this.obscure = false});
  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      obscureText: obscure,
      decoration: InputDecoration(labelText: label, border: const OutlineInputBorder()),
    );
  }
}
```

```dart
// path: lib/core/widgets/empty_state.dart
import 'package:flutter/material.dart';
class EmptyState extends StatelessWidget {
  final String message;
  final VoidCallback? onAction;
  final String? actionLabel;
  const EmptyState({super.key, required this.message, this.onAction, this.actionLabel});
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(mainAxisSize: MainAxisSize.min, children: [
        const Icon(Icons.energy_savings_leaf_outlined, size: 64),
        const SizedBox(height: 8),
        Text(message, textAlign: TextAlign.center),
        if (onAction != null) ...[
          const SizedBox(height: 12),
          OutlinedButton(onPressed: onAction, child: Text(actionLabel ?? 'Qayta urinish')),
        ],
      ]),
    );
  }
}
```

```dart
// path: lib/core/widgets/loading_overlay.dart
import 'package:flutter/material.dart';

class LoadingOverlay extends StatelessWidget {
  final bool show;
  final Widget child;
  const LoadingOverlay({super.key, required this.show, required this.child});
  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      child,
      if (show)
        Positioned.fill(
          child: ColoredBox(
            color: Colors.black38,
            child: Center(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(color: Theme.of(context).colorScheme.surface, borderRadius: BorderRadius.circular(12)),
                child: const Row(mainAxisSize: MainAxisSize.min, children: [
                  SizedBox(height: 20, width: 20, child: CircularProgressIndicator(strokeWidth: 2)),
                  SizedBox(width: 12),
                  Text('Yuklanmoqda...'),
                ]),
              ),
            ),
          ),
        ),
    ]);
  }
}
```

---

## Auth (API, Local, Repo, Providers, Views)

```dart
// path: lib/features/auth/data/auth_api.dart
import 'package:dio/dio.dart';
import '../../../core/error/app_exception.dart';

class AuthApi {
  final Dio dio;
  AuthApi(this.dio);

  Future<(String access, String refresh, bool mustChange)> login(String username, String password) async {
    try {
      final res = await dio.post('/auth/login', data: {'username': username, 'password': password});
      final d = res.data as Map;
      return (d['accessToken'] as String, d['refreshToken'] as String, (d['mustChangePassword'] ?? false) as bool);
    } on DioException catch (e) {
      throw AppException(e.response?.data?['message']?.toString() ?? 'Login xatosi', statusCode: e.response?.statusCode);
    }
  }

  Future<(String access, String refresh)> refresh(String refreshToken) async {
    try {
      final res = await dio.post('/auth/refresh', data: {'refreshToken': refreshToken});
      final d = res.data as Map;
      return (d['accessToken'] as String, d['refreshToken'] as String);
    } on DioException catch (_) {
      throw AppException.auth('Refresh xatosi');
    }
  }

  Future<void> changePassword(String oldPwd, String newPwd) async {
    try {
      await dio.post('/auth/change-password', data: {'oldPassword': oldPwd, 'newPassword': newPwd});
    } on DioException catch (e) {
      throw AppException(e.response?.data?['message']?.toString() ?? 'Parol almashtirish xatosi');
    }
  }
}
```

```dart
// path: lib/features/auth/data/auth_local.dart
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class AuthLocal {
  final FlutterSecureStorage _sec = const FlutterSecureStorage();
  static const _kAccess = 'access_token';
  static const _kRefresh = 'refresh_token';

  Future<void> saveTokens(String access, String refresh) async {
    await _sec.write(key: _kAccess, value: access);
    await _sec.write(key: _kRefresh, value: refresh);
  }

  Future<String?> getAccess() async => _sec.read(key: _kAccess);
  Future<String?> getRefresh() async => _sec.read(key: _kRefresh);

  Future<void> clear() async {
    await _sec.deleteAll();
  }
}
```

```dart
// path: lib/features/auth/repo/auth_repo.dart
import 'package:dio/dio.dart';
import '../../../core/env/env.dart';
import '../../../core/network/dio_client.dart';
import '../data/auth_api.dart';
import '../data/auth_local.dart';

class AuthRepo {
  final AuthLocal local;
  late final AuthApi api;

  AuthRepo._(this.local, Dio dio) {
    api = AuthApi(dio);
  }

  static Future<AuthRepo> create(AuthLocal local, Future<bool> Function() tryRefresh, Future<String?> Function() getAccess, Future<String?> Function() getRefresh) async {
    final dio = DioClient.build(getAccessToken: getAccess, getRefreshToken: getRefresh, tryRefresh: tryRefresh);
    return AuthRepo._(local, dio);
  }

  Future<(bool mustChange, String? error)> login(String u, String p) async {
    if (Env.useMock) {
      await local.saveTokens('mock_access', 'mock_refresh');
      return (false, null);
    }
    final (access, refresh, mustChange) = await api.login(u, p);
    await local.saveTokens(access, refresh);
    return (mustChange, null);
  }

  Future<bool> refresh() async {
    if (Env.useMock) {
      await local.saveTokens('mock_access', 'mock_refresh');
      return true;
    }
    final r = await local.getRefresh();
    if (r == null) return false;
    try {
      final (a, n) = await api.refresh(r);
      await local.saveTokens(a, n);
      return true;
    } catch (_) {
      await logout();
      return false;
    }
  }

  Future<void> logout() async {
    await local.clear();
  }

  Future<void> changePassword(String oldP, String newP) async {
    if (Env.useMock) return;
    await api.changePassword(oldP, newP);
  }
}
```

```dart
// path: lib/features/auth/providers/auth_providers.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../data/auth_local.dart';
import '../repo/auth_repo.dart';

final authLocalProvider = Provider<AuthLocal>((ref) => AuthLocal());

final tokensProvider = StateProvider<(String? access, String? refresh)>((ref) => (null, null));

final authRepoProvider = Provider<AuthRepo>((ref) {
  final local = ref.read(authLocalProvider);
  Future<String?> getAccess() async => (await local.getAccess());
  Future<String?> getRefresh() async => (await local.getRefresh());
  Future<bool> tryRefresh() async => (await ref.read(authRepoProvider).refresh());
  throw UnimplementedError('Initialized in authInitProvider');
});

final authInitProvider = FutureProvider<void>((ref) async {
  final local = ref.read(authLocalProvider);
  Future<String?> getAccess() async => (await local.getAccess());
  Future<String?> getRefresh() async => (await local.getRefresh());
  late AuthRepo repo;
  repo = await AuthRepo.create(local, () async => await repo.refresh(), getAccess, getRefresh);
  ref.onDispose(() {});
  ref.keepAlive();
  ref.state = ref.state; // no-op
  // Hack to put repo in container:
  // ignore: invalid_use_of_internal_member
  ref.container.updateOverrides([authRepoProvider.overrideWithValue(repo)]);
});
```

```dart
// path: lib/features/auth/view/login_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/widgets/app_button.dart';
import '../../../core/widgets/app_text_field.dart';
import '../../../core/utils/validators.dart';
import '../../../core/env/env.dart';
import '../../orders/view/orders_list_page.dart';
import '../providers/auth_providers.dart';

class LoginPage extends ConsumerStatefulWidget {
  final Object flavor;
  const LoginPage({super.key, required this.flavor});

  @override
  ConsumerState<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<LoginPage> {
  final _form = GlobalKey<FormState>();
  final _u = TextEditingController();
  final _p = TextEditingController();
  bool _loading = false;
  String? _error;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(Env.appName)),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _form,
          child: Column(
            children: [
              AppTextField(controller: _u, label: 'Login', validator: Validators.requiredField),
              const SizedBox(height: 12),
              AppTextField(controller: _p, label: 'Parol', obscure: true, validator: Validators.requiredField),
              const SizedBox(height: 12),
              if (_error != null) Text(_error!, style: TextStyle(color: Theme.of(context).colorScheme.error)),
              const Spacer(),
              AppButton(
                label: _loading ? '...' : 'Kirish',
                loading: _loading,
                onPressed: () async {
                  if (!_form.currentState!.validate()) return;
                  setState(() => _loading = true);
                  final repo = ref.read(authRepoProvider);
                  try {
                    final (mustChange, error) = await repo.login(_u.text.trim(), _p.text.trim());
                    if (error != null) throw Exception(error);
                    if (mustChange) {
                      if (mounted) Navigator.pushReplacementNamed(context, '/change-password');
                    } else {
                      if (mounted) Navigator.pushReplacementNamed(context, '/orders');
                    }
                  } catch (e) {
                    setState(() => _error = e.toString());
                  } finally {
                    if (mounted) setState(() => _loading = false);
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

```dart
// path: lib/features/auth/view/change_password_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/widgets/app_button.dart';
import '../../../core/widgets/app_text_field.dart';
import '../../../core/utils/validators.dart';
import '../providers/auth_providers.dart';

class ChangePasswordPage extends ConsumerStatefulWidget {
  const ChangePasswordPage({super.key});
  @override
  ConsumerState<ChangePasswordPage> createState() => _ChangePasswordPageState();
}

class _ChangePasswordPageState extends ConsumerState<ChangePasswordPage> {
  final _form = GlobalKey<FormState>();
  final _old = TextEditingController();
  final _new = TextEditingController();
  bool _loading = false;
  String? _msg;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Parolni almashtirish')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _form,
          child: Column(children: [
            AppTextField(controller: _old, label: 'Eski parol', obscure: true, validator: Validators.requiredField),
            const SizedBox(height: 12),
            AppTextField(controller: _new, label: 'Yangi parol', obscure: true, validator: Validators.requiredField),
            const Spacer(),
            if (_msg != null) Text(_msg!),
            AppButton(
              label: _loading ? '...' : 'Saqlash',
              loading: _loading,
              onPressed: () async {
                if (!_form.currentState!.validate()) return;
                setState(() => _loading = true);
                try {
                  await ref.read(authRepoProvider).changePassword(_old.text, _new.text);
                  if (mounted) setState(() => _msg = 'Parol yangilandi');
                } catch (e) {
                  if (mounted) setState(() => _msg = e.toString());
                } finally {
                  if (mounted) setState(() => _loading = false);
                }
              },
            ),
          ]),
        ),
      ),
    );
  }
}
```

---

## Orders (models, API, local cache, repo, providers, views)

```dart
// path: lib/features/orders/models/order_models.dart
class OrderItem {
  final String treeId;
  final String treeName;
  final int qty;
  final int price; // so'm
  OrderItem({required this.treeId, required this.treeName, required this.qty, required this.price});
  int get total => qty * price;

  Map<String, dynamic> toJson() => {'treeId': treeId, 'treeName': treeName, 'qty': qty, 'price': price};
  factory OrderItem.fromJson(Map<String, dynamic> j) => OrderItem(treeId: j['treeId'], treeName: j['treeName'] ?? '', qty: j['qty'] ?? 1, price: j['price'] ?? 0);
}

class OrderModel {
  final String id;
  final DateTime createdAt;
  final String customerName;
  final String phone;
  final String address;
  final double? lat;
  final double? lng;
  final List<String> photos;     // local file paths or remote urls
  final String? doorPhoto;
  final String? signaturePath;
  final String paymentMethod;    // cash | credit
  final List<OrderItem> items;
  final String status;           // pending|sent|completed
  final String? contractUrl;

  OrderModel({
    required this.id,
    required this.createdAt,
    required this.customerName,
    required this.phone,
    required this.address,
    this.lat,
    this.lng,
    this.photos = const [],
    this.doorPhoto,
    this.signaturePath,
    required this.paymentMethod,
    required this.items,
    this.status = 'pending',
    this.contractUrl,
  });

  int get total => items.fold(0, (s, e) => s + e.total);

  Map<String, dynamic> toJson() => {
        'id': id,
        'createdAt': createdAt.toIso8601String(),
        'customerName': customerName,
        'phone': phone,
        'address': address,
        'lat': lat,
        'lng': lng,
        'photos': photos,
        'doorPhoto': doorPhoto,
        'signaturePath': signaturePath,
        'paymentMethod': paymentMethod,
        'items': items.map((e) => e.toJson()).toList(),
        'status': status,
        'contractUrl': contractUrl,
      };

  factory OrderModel.fromJson(Map<String, dynamic> j) => OrderModel(
        id: j['id'],
        createdAt: DateTime.tryParse(j['createdAt'] ?? '') ?? DateTime.now(),
        customerName: j['customerName'] ?? '',
        phone: j['phone'] ?? '',
        address: j['address'] ?? '',
        lat: (j['lat'] as num?)?.toDouble(),
        lng: (j['lng'] as num?)?.toDouble(),
        photos: (j['photos'] as List?)?.cast<String>() ?? const [],
        doorPhoto: j['doorPhoto'],
        signaturePath: j['signaturePath'],
        paymentMethod: j['paymentMethod'] ?? 'cash',
        items: ((j['items'] as List?) ?? []).map((e) => OrderItem.fromJson(Map<String, dynamic>.from(e))).toList(),
        status: j['status'] ?? 'pending',
        contractUrl: j['contractUrl'],
      );
}
```

```dart
// path: lib/features/orders/data/orders_api.dart
import 'dart:io';
import 'package:dio/dio.dart';
import '../../../core/env/env.dart';
import '../../../core/error/app_exception.dart';
import '../models/order_models.dart';

class OrdersApi {
  final Dio dio;
  OrdersApi(this.dio);

  Future<List<OrderModel>> getMyOrders({int page = 1, int limit = 20, DateTime? from, DateTime? to}) async {
    if (Env.useMock) {
      // Simple mock
      await Future.delayed(const Duration(milliseconds: 400));
      return List.generate(3, (i) => OrderModel(
        id: 'MOCK-${DateTime.now().millisecondsSinceEpoch}-$i',
        createdAt: DateTime.now().subtract(Duration(days: i)),
        customerName: 'Fuqaro $i',
        phone: '+99890000000$i',
        address: 'Toshkent, ko‘cha $i',
        paymentMethod: 'cash',
        items: [OrderItem(treeId: 't$i', treeName: 'Olma ($i)', qty: 1+i, price: 50000)],
      ));
    }
    try {
      final res = await dio.get('/orders', queryParameters: {
        'mine': true,
        'page': page,
        'limit': limit,
        if (from != null) 'from': from.toIso8601String(),
        if (to != null) 'to': to.toIso8601String(),
      });
      final list = (res.data as List).map((e) => OrderModel.fromJson(Map<String, dynamic>.from(e))).toList();
      return list;
    } on DioException catch (e) {
      throw AppException(e.message ?? 'Orders yuklash xatosi', statusCode: e.response?.statusCode);
    }
  }

  Future<OrderModel> createOrder(OrderModel order) async {
    if (Env.useMock) {
      await Future.delayed(const Duration(milliseconds: 300));
      return order; // mock: echo back
    }
    final form = FormData.fromMap({
      'customer': {
        'name': order.customerName,
        'phone': order.phone,
        'address': order.address,
      },
      'media': {
        'lat': order.lat,
        'lng': order.lng,
      },
      'payment': {
        'method': order.paymentMethod,
      },
      'items': order.items.map((e) => e.toJson()).toList(),
      if (order.signaturePath != null) 'signature': await MultipartFile.fromFile(order.signaturePath!, filename: 'signature.png'),
      for (int i = 0; i < order.photos.length; i++)
        'photo$i': await MultipartFile.fromFile(order.photos[i], filename: 'photo_$i.jpg'),
    });
    try {
      final res = await dio.post('/orders', data: form);
      return OrderModel.fromJson(Map<String, dynamic>.from(res.data));
    } on DioException catch (e) {
      throw AppException(e.response?.data?['message']?.toString() ?? 'Yaratish xatosi', statusCode: e.response?.statusCode);
    }
  }

  Future<OrderModel> getById(String id) async {
    if (Env.useMock) {
      await Future.delayed(const Duration(milliseconds: 200));
      return OrderModel(
        id: id,
        createdAt: DateTime.now(),
        customerName: 'Demo',
        phone: '+99890XXXXXXX',
        address: 'Demo manzil',
        paymentMethod: 'cash',
        items: [OrderItem(treeId: 't1', treeName: 'Olma', qty: 2, price: 50000)],
        status: 'sent',
        contractUrl: 'https://example.com/contract.pdf',
      );
    }
    try {
      final res = await dio.get('/orders/$id');
      return OrderModel.fromJson(Map<String, dynamic>.from(res.data));
    } on DioException catch (e) {
      throw AppException(e.message ?? 'Order topilmadi', statusCode: e.response?.statusCode);
    }
  }
}
```

```dart
// path: lib/features/orders/data/orders_local.dart
import 'package:hive_flutter/hive_flutter.dart';
import '../models/order_models.dart';

class OrdersLocal {
  static const _boxOrders = 'orders_cache';
  static const _boxQueue = 'orders_queue';

  Future<void> init() async {
    await Hive.initFlutter();
    await Hive.openBox(_boxOrders);
    await Hive.openBox(_boxQueue);
  }

  Future<List<OrderModel>> getCached() async {
    final box = Hive.box(_boxOrders);
    final list = (box.values.cast<Map>().toList()).map((e) => OrderModel.fromJson(Map<String, dynamic>.from(e))).toList();
    list.sort((a,b)=>b.createdAt.compareTo(a.createdAt));
    return list;
  }

  Future<void> saveCache(List<OrderModel> orders) async {
    final box = Hive.box(_boxOrders);
    await box.clear();
    for (final o in orders) {
      await box.put(o.id, o.toJson());
    }
  }

  Future<void> upsertOrder(OrderModel o) async {
    final box = Hive.box(_boxOrders);
    await box.put(o.id, o.toJson());
  }

  Future<void> enqueue(OrderModel o) async {
    final box = Hive.box(_boxQueue);
    await box.put(o.id, o.toJson());
  }

  Future<List<OrderModel>> getQueue() async {
    final box = Hive.box(_boxQueue);
    return box.values.map((e) => OrderModel.fromJson(Map<String, dynamic>.from(e))).toList();
  }

  Future<void> dequeue(String id) async {
    final box = Hive.box(_boxQueue);
    await box.delete(id);
  }
}
```

```dart
// path: lib/features/orders/repo/orders_repo.dart
import 'package:dio/dio.dart';
import '../../../core/network/dio_client.dart';
import '../../auth/data/auth_local.dart';
import '../data/orders_api.dart';
import '../data/orders_local.dart';
import '../models/order_models.dart';

class OrdersRepo {
  final OrdersApi api;
  final OrdersLocal local;

  OrdersRepo._(this.api, this.local);

  static Future<OrdersRepo> create(AuthLocal auth, OrdersLocal local, Future<bool> Function() tryRefresh) async {
    final dio = DioClient.build(
      getAccessToken: () => auth.getAccess(),
      getRefreshToken: () => auth.getRefresh(),
      tryRefresh: tryRefresh,
    );
    await local.init();
    return OrdersRepo._(OrdersApi(dio), local);
  }

  Future<List<OrderModel>> list({int page = 1}) async {
    final cached = await local.getCached();
    try {
      final remote = await api.getMyOrders(page: page);
      await local.saveCache(remote);
      return remote;
    } catch (_) {
      return cached;
    }
  }

  Future<void> queueCreate(OrderModel o) async {
    await local.enqueue(o);
    await local.upsertOrder(o);
  }

  Future<OrderModel> getById(String id) async {
    try {
      final r = await api.getById(id);
      await local.upsertOrder(r);
      return r;
    } catch (_) {
      final cached = (await local.getCached()).where((e) => e.id == id).toList();
      if (cached.isNotEmpty) return cached.first;
      rethrow;
    }
  }

  Future<OrderModel> sendNow(OrderModel o) async {
    final sent = await api.createOrder(o);
    await local.upsertOrder(sent);
    await local.dequeue(o.id);
    return sent;
  }

  Future<List<OrderModel>> getQueue() => local.getQueue();
  Future<void> dequeue(String id) => local.dequeue(id);
}
```

```dart
// path: lib/features/orders/providers/order_providers.dart
import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../auth/providers/auth_providers.dart';
import '../data/orders_local.dart';
import '../models/order_models.dart';
import '../repo/orders_repo.dart';

final ordersLocalProvider = Provider<OrdersLocal>((ref) => OrdersLocal());

final ordersRepoProvider = Provider<OrdersRepo>((ref) {
  throw UnimplementedError('Initialized in ordersInitProvider');
});

final ordersInitProvider = FutureProvider<void>((ref) async {
  await ref.read(authInitProvider.future); // ensure auth repo exists
  final authRepo = ref.read(authRepoProvider);
  final local = ref.read(ordersLocalProvider);
  final repo = await OrdersRepo.create(ref.read(authLocalProvider), local, () async => await authRepo.refresh());
  // ignore: invalid_use_of_internal_member
  ref.container.updateOverrides([ordersRepoProvider.overrideWithValue(repo)]);
});

final ordersListProvider = FutureProvider<List<OrderModel>>((ref) async {
  await ref.read(ordersInitProvider.future);
  final repo = ref.read(ordersRepoProvider);
  return repo.list();
});

final orderByIdProvider = FutureProvider.family<OrderModel, String>((ref, id) async {
  await ref.read(ordersInitProvider.future);
  final repo = ref.read(ordersRepoProvider);
  return repo.getById(id);
});

class CreateOrderState {
  String customerName = '';
  String phone = '';
  String address = '';
  double? lat;
  double? lng;
  List<String> photos = [];
  String? doorPhoto;
  String paymentMethod = 'cash';
  String? signaturePath;
  final List<OrderItem> items = [];
}

final createOrderProvider = StateProvider<CreateOrderState>((ref) => CreateOrderState());
```

```dart
// path: lib/features/orders/view/orders_list_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/widgets/empty_state.dart';
import '../../../core/utils/date_time.dart';
import '../providers/order_providers.dart';

class OrdersListPage extends ConsumerWidget {
  const OrdersListPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final asyncOrders = ref.watch(ordersListProvider);
    return Scaffold(
      appBar: AppBar(title: const Text('Buyurtmalar')),
      body: asyncOrders.when(
        data: (orders) => orders.isEmpty
            ? const EmptyState(message: 'Buyurtmalar yo‘q')
            : ListView.builder(
                itemCount: orders.length,
                itemBuilder: (c, i) {
                  final o = orders[i];
                  return ListTile(
                    leading: o.doorPhoto != null ? Image.asset(o.doorPhoto!, width: 48, height: 48, fit: BoxFit.cover) : const Icon(Icons.receipt_long),
                    title: Text(o.customerName),
                    subtitle: Text('${DateTimeUtils.formatDateTime(o.createdAt)} • ${o.phone}'),
                    trailing: Text('${o.total ~/ 1000}k'),
                    onTap: () => Navigator.pushNamed(context, '/orders/${o.id}'),
                  );
                },
              ),
        error: (e, _) => EmptyState(message: e.toString(), onAction: () => ref.refresh(ordersListProvider)),
        loading: () => const Center(child: CircularProgressIndicator()),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => Navigator.pushNamed(context, '/orders/create'),
        icon: const Icon(Icons.add),
        label: const Text('Yaratish'),
      ),
    );
  }
}
```

```dart
// path: lib/features/orders/view/order_create_page.dart
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/utils/validators.dart';
import '../../../core/widgets/app_button.dart';
import '../../media/services/geolocator_service.dart';
import '../../media/services/media_picker.dart';
import '../../media/services/signature_service.dart';
import '../models/order_models.dart';
import '../providers/order_providers.dart';
import '../repo/orders_repo.dart';

class OrderCreatePage extends ConsumerStatefulWidget {
  const OrderCreatePage({super.key});
  @override
  ConsumerState<OrderCreatePage> createState() => _OrderCreatePageState();
}

class _OrderCreatePageState extends ConsumerState<OrderCreatePage> {
  final _form = GlobalKey<FormState>();
  final _name = TextEditingController();
  final _phone = TextEditingController();
  final _address = TextEditingController();
  final _qty = TextEditingController(text: '1');
  bool _sending = false;
  String _method = 'cash';
  String? _signaturePath;
  final List<OrderItem> _items = [];
  final List<String> _photos = [];
  String? _doorPhoto;

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(createOrderProvider);
    return Scaffold(
      appBar: AppBar(title: const Text('Buyurtma yaratish')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _form,
          child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text('Fuqaro ma’lumotlari', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            TextFormField(controller: _name, decoration: const InputDecoration(labelText: 'F.I.Sh', border: OutlineInputBorder()), validator: Validators.requiredField),
            const SizedBox(height: 8),
            TextFormField(controller: _phone, decoration: const InputDecoration(labelText: 'Telefon', border: OutlineInputBorder()), validator: Validators.phone, keyboardType: TextInputType.phone),
            const SizedBox(height: 8),
            TextFormField(controller: _address, decoration: const InputDecoration(labelText: 'Manzil', border: OutlineInputBorder()), validator: Validators.requiredField),
            const SizedBox(height: 16),

            Text('Media & Geo', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            Wrap(spacing: 8, runSpacing: 8, children: [
              FilledButton.icon(onPressed: () async {
                final p = await MediaPicker.pickAndCompressImage();
                if (p != null) setState(()=> _photos.add(p));
              }, icon: const Icon(Icons.photo_camera), label: const Text('Ko‘chat fotosi')),
              OutlinedButton.icon(onPressed: () async {
                final p = await MediaPicker.pickAndCompressImage();
                if (p != null) setState(()=> _doorPhoto = p);
              }, icon: const Icon(Icons.door_front_door_outlined), label: const Text('Eshik foto')),
              OutlinedButton.icon(onPressed: () async {
                final pos = await GeolocatorService.getCurrent();
                if (mounted && pos != null) {
                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Geo: ${pos.latitude}, ${pos.longitude}')));
                  state.lat = pos.latitude; state.lng = pos.longitude;
                }
              }, icon: const Icon(Icons.my_location), label: const Text('Geo olish')),
            ]),
            const SizedBox(height: 8),
            if (_photos.isNotEmpty) SizedBox(height: 80, child: ListView.separated(scrollDirection: Axis.horizontal, itemBuilder: (_,i)=>Image.asset(_photos[i], width: 80, height: 80, fit: BoxFit.cover), separatorBuilder: (_, __)=>const SizedBox(width: 8), itemCount: _photos.length)),

            const SizedBox(height: 16),
            Text('Ko‘chatlar', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            Row(children: [
              Expanded(child: TextFormField(decoration: const InputDecoration(labelText: 'Nom (masalan: Olma)', border: OutlineInputBorder()), validator: Validators.requiredField, onSaved: (v){
                // stored via local var
              })),
              const SizedBox(width: 8),
              SizedBox(width: 100, child: TextFormField(controller: _qty, decoration: const InputDecoration(labelText: 'Qty', border: OutlineInputBorder()), validator: Validators.qty, keyboardType: TextInputType.number)),
              const SizedBox(width: 8),
              SizedBox(width: 120, child: TextFormField(initialValue: '50000', decoration: const InputDecoration(labelText: 'Narx', border: OutlineInputBorder()), keyboardType: TextInputType.number, onSaved: (v){})),
              const SizedBox(width: 8),
              FilledButton.icon(onPressed: (){
                final q = int.tryParse(_qty.text)??1;
                final price = 50000;
                setState(()=> _items.add(OrderItem(treeId: 'tree-${_items.length+1}', treeName: 'Olma', qty: q, price: price)));
              }, icon: const Icon(Icons.add), label: const Text('Qo‘shish')),
            ]),
            const SizedBox(height: 8),
            if (_items.isNotEmpty)
              Column(children: _items.map((e)=>ListTile(
                title: Text('${e.treeName} x${e.qty}'),
                trailing: Text('${e.total ~/ 1000}k'),
              )).toList()),

            const SizedBox(height: 16),
            Text('To‘lov', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            SegmentedButton<String>(
              segments: const [
                ButtonSegment(value:'cash', label: Text('Naqd')),
                ButtonSegment(value:'credit', label: Text('Kredit')),
              ],
              selected: {_method},
              onSelectionChanged: (s)=> setState(()=> _method = s.first),
            ),
            const SizedBox(height: 8),
            if (_method == 'cash' || _method == 'credit') OutlinedButton.icon(
              onPressed: () async {
                final p = await SignatureService.captureSignature(context);
                if (p != null) setState(()=> _signaturePath = p);
              }, icon: const Icon(Icons.gesture), label: Text(_signaturePath==null ? 'Imzo olish' : 'Imzo olingan'),
            ),

            const SizedBox(height: 24),
            Align(
              alignment: Alignment.centerRight,
              child: AppButton(label: _sending ? '...' : 'Saqlash (offline queue)', loading: _sending, onPressed: _onSubmit),
            ),
            const SizedBox(height: 24),
          ]),
        ),
      ),
    );
  }

  Future<void> _onSubmit() async {
    if (!_form.currentState!.validate()) return;
    setState(()=> _sending = true);
    try {
      final id = 'L${DateTime.now().millisecondsSinceEpoch}-${Random().nextInt(999)}';
      final order = OrderModel(
        id: id,
        createdAt: DateTime.now(),
        customerName: _name.text.trim(),
        phone: _phone.text.trim(),
        address: _address.text.trim(),
        lat: ref.read(createOrderProvider).lat,
        lng: ref.read(createOrderProvider).lng,
        photos: _photos,
        doorPhoto: _doorPhoto,
        signaturePath: _signaturePath,
        paymentMethod: _method,
        items: _items.isNotEmpty ? _items : [OrderItem(treeId: 't1', treeName: 'Olma', qty: 1, price: 50000)],
        status: 'pending',
      );
      final repo = ref.read(ordersRepoProvider);
      await repo.queueCreate(order);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Saqlanib qo‘yildi, internet bo‘lganda yuboriladi')));
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(e.toString())));
    } finally {
      if (mounted) setState(()=> _sending = false);
    }
  }
}
```

```dart
// path: lib/features/orders/view/order_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../core/widgets/empty_state.dart';
import '../../../core/utils/date_time.dart';
import '../providers/order_providers.dart';

class OrderDetailPage extends ConsumerWidget {
  final String orderId;
  const OrderDetailPage({super.key, required this.orderId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final asyncOrder = ref.watch(orderByIdProvider(orderId));
    return Scaffold(
      appBar: AppBar(title: const Text('Buyurtma')),
      body: asyncOrder.when(
        data: (o) => ListView(
          padding: const EdgeInsets.all(16),
          children: [
            ListTile(title: Text(o.customerName), subtitle: Text(o.phone)),
            ListTile(title: const Text('Sana'), subtitle: Text(DateTimeUtils.formatDateTime(o.createdAt))),
            ListTile(title: const Text('Manzil'), subtitle: Text(o.address)),
            ListTile(title: const Text('To‘lov turi'), subtitle: Text(o.paymentMethod)),
            ListTile(title: const Text('Jami'), trailing: Text('${o.total} so‘m')),
            const Divider(),
            const Text('Tovarlar', style: TextStyle(fontWeight: FontWeight.bold)),
            ...o.items.map((e)=>ListTile(
              dense: true,
              title: Text('${e.treeName} x${e.qty}'),
              trailing: Text('${e.total}'),
            )),
            const SizedBox(height: 12),
            if (o.contractUrl != null)
              FilledButton.icon(
                onPressed: () async { final uri = Uri.parse(o.contractUrl!); if (await canLaunchUrl(uri)) await launchUrl(uri, mode: LaunchMode.externalApplication); },
                icon: const Icon(Icons.picture_as_pdf_outlined),
                label: const Text('PDF shartnoma'),
              ),
          ],
        ),
        error: (e, _) => EmptyState(message: e.toString()),
        loading: () => const Center(child: CircularProgressIndicator()),
      ),
    );
  }
}
```

```dart
// path: lib/features/orders/widgets/order_item_tile.dart
import 'package:flutter/material.dart';
import '../models/order_models.dart';

class OrderItemTile extends StatelessWidget {
  final OrderItem item;
  const OrderItemTile({super.key, required this.item});
  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(item.treeName),
      subtitle: Text('Qty: ${item.qty}'),
      trailing: Text('${item.total} so‘m'),
    );
  }
}
```

> `areas/*` va `trees/*` modullari uchun API/Local/Repo fayllarini xuddi shu uslubda qo‘shasiz (dependent dropdownlar uchun). Dev bosqichida oddiy ro‘yxatlarni mahalliy listlardan to‘ldirib turish mumkin.

---

## Media & Geo servislar

```dart
// path: lib/features/media/services/media_picker.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';

class MediaPicker {
  static Future<String?> pickAndCompressImage({ImageSource source = ImageSource.camera}) async {
    final picker = ImagePicker();
    final x = await picker.pickImage(source: source, imageQuality: 100);
    if (x == null) return null;
    final dir = await getApplicationDocumentsDirectory();
    final outPath = '${dir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
    final result = await FlutterImageCompress.compressAndGetFile(x.path, outPath, quality: 85, minWidth: 1080, minHeight: 1080);
    return result?.path ?? x.path;
  }
}
```

```dart
// path: lib/features/media/services/geolocator_service.dart
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

class GeolocatorService {
  static Future<Position?> getCurrent() async {
    final p = await Permission.locationWhenInUse.request();
    if (!p.isGranted) return null;
    final enabled = await Geolocator.isLocationServiceEnabled();
    if (!enabled) return null;
    return Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
  }
}
```

```dart
// path: lib/features/media/services/signature_service.dart
import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:signature/signature.dart';
import 'package:path_provider/path_provider.dart';

class SignatureService {
  static Future<String?> captureSignature(BuildContext context) async {
    final sigController = SignatureController(penStrokeWidth: 3, penColor: Colors.black);
    final path = await Navigator.of(context).push<String>(MaterialPageRoute(builder: (_) => _SignaturePage(controller: sigController)));
    return path;
  }
}

class _SignaturePage extends StatefulWidget {
  final SignatureController controller;
  const _SignaturePage({required this.controller});
  @override
  State<_SignaturePage> createState() => _SignaturePageState();
}

class _SignaturePageState extends State<_SignaturePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Imzo')),
      body: Column(children: [
        Expanded(child: Container(color: Colors.white, child: Signature(controller: widget.controller, backgroundColor: Colors.white))),
        Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
          TextButton(onPressed: ()=> widget.controller.clear(), child: const Text('Tozalash')),
          FilledButton(onPressed: _save, child: const Text('Saqlash')),
        ]),
        const SizedBox(height: 12),
      ]),
    );
  }

  Future<void> _save() async {
    final data = await widget.controller.toImage();
    if (data == null) return;
    final byteData = await data.toByteData(format: ui.ImageByteFormat.png);
    if (byteData == null) return;
    final dir = await getApplicationDocumentsDirectory();
    final file = File('${dir.path}/sig_${DateTime.now().millisecondsSinceEpoch}.png');
    await file.writeAsBytes(byteData.buffer.asUint8List());
    if (mounted) Navigator.pop(context, file.path);
  }
}
```

---

## Sync (offline queue → server)

```dart
// path: lib/features/sync/services/sync_service.dart
import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../orders/repo/orders_repo.dart';
import '../../orders/models/order_models.dart';

class SyncService {
  final OrdersRepo repo;
  StreamSubscription? _sub;

  SyncService(this.repo);

  void start() {
    _sub = Connectivity().onConnectivityChanged.listen((_) => _trySync());
    _trySync();
  }

  void dispose() {
    _sub?.cancel();
  }

  Future<void> _trySync() async {
    final con = await Connectivity().checkConnectivity();
    if (con == ConnectivityResult.none) return;
    final queue = await repo.getQueue();
    for (final o in queue) {
      try {
        await repo.sendNow(o);
      } catch (_) {
        // leave in queue; will retry later
      }
    }
  }
}
```

```dart
// path: lib/features/sync/providers/sync_providers.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/sync_service.dart';
import '../../orders/providers/order_providers.dart';

final syncServiceProvider = Provider<SyncService>((ref) {
  final repo = ref.read(ordersRepoProvider);
  final s = SyncService(repo)..start();
  ref.onDispose(s.dispose);
  return s;
});
```

---

# Android/iOS ruxsatlar

**AndroidManifest.xml** (asosiy ruxsatlar):

```xml
<!-- path: android/app/src/main/AndroidManifest.xml (relevant snippet) -->
<uses-permission android:name="android.permission.INTERNET"/>
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
<uses-permission android:name="android.permission.CAMERA"/>
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
<!-- SDK<33 uchun: -->
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32"/>
```

**iOS Info.plist**:

```xml
<!-- path: ios/Runner/Info.plist (relevant keys) -->
<key>NSCameraUsageDescription</key>
<string>Kamera ruxsati kerak</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>Galereyadan rasm tanlash uchun ruxsat</string>
<key>NSLocationWhenInUseUsageDescription</key>
<string>Geo-joylashuv kerak</string>
```

---

## O‘rnatish va ishga tushirish (README qisqa)

1. **Repo tayyorlash**

* yuqoridagi fayllarni mos papkalarga joylang.

2. **Dependensiyalar**

```bash
flutter pub get
```

3. **Env**

* Dev: `.env` faylini loyihaning ildiziga qo‘ying (namuna yuqorida).
* Prod: `.env.prod` yarating (USE\_MOCK=false, prod API URL).

4. **Run (Emulator/Device)**

```bash
# Android (dev flavor)
flutter run -t lib/main_dev.dart

# iOS (dev flavor)
open ios/Runner.xcworkspace  # Xcode orqali run
```

5. **Build**

```bash
flutter analyze
flutter test
flutter build apk -t lib/main_prod.dart
flutter build appbundle -t lib/main_prod.dart
```

---

## Smoke Test (qo‘lda)

1. Ilovani ishga tushiring (dev).
2. **Login**: istalgan login/parol (dev’da `USE_MOCK=true` bo‘lgani uchun kiraveradi).
3. **Orders List**: 2–3 ta mock buyurtma ko‘rinadi.
4. **Create Order**:

   * F.I.Sh, telefon, manzil kiriting.
   * “Ko‘chat fotosi” va “Eshik foto” tanlang (kamera yoki galereya).
   * “Geo olish” bosing (ruxsat berib, koordinata datang).
   * “Ko‘shish” bilan kamida 1 item.
   * To‘lov turi `Naqd` va “Imzo olish”.
   * “Saqlash (offline queue)” bosing — snackbar chiqadi.
5. **Offline rejim**:

   * Qurilmada internetni o‘chiring.
   * Yana 1–2 ta order saqlang — queue’da qoladi.
   * Internetni yoqing — bir necha soniyada auto-sync bo‘ladi (dev’da mock jo‘natadi, prod’da backendga POST).
6. **Order Details**: ro‘yxatdan buyurtmani oching, “PDF shartnoma” tugmasi url\_launcher bilan tashqi brauzerda ochiladi (mock URL bo‘lsa ham).

---

## Eslatmalar

* **Backend JWT**: Interceptor Authorization header qo‘shadi; 401’da refresh urinish bor. Mock rejimda tokenlar “mock\_\*”.
* **Cache**: Hive’da `orders_cache` (ro‘yxat) va `orders_queue` (jo‘natilmagan buyurtmalar).
* **Geolokatsiya/Media/Signature**: minimal, ishchi namunalar. Siqish 85% sifat, \~1080px.
* **L10n**: `flutter_localizations` ulangan (uz/ru). Zarurat bo‘lsa keyin `gen-l10n` bilan matnlarni chiqaramiz.
* **Areas/Trees**: hozir UI’da minimal. Real dependent-dropdownlar uchun `features/areas/*` va `features/trees/*` ni yuqoridagi pattern bilan to‘ldiring (startda pull + local cache).
* **OTP flow**: kredit to‘lovida UI hook bor; backend tayyor bo‘lgach `/otp/send` va `/otp/verify` ni OrdersCreate ichida qo‘shish oson (dio bilan 2 tugma).

---

Shu skelet real loyihaga tayyor: mock bilan devda ishlaydi, prod’da esa darrov backendga ulanadi. Keyingi bosqichda **Areas/Trees dependent dropdown** va **OTP verify** qismlarini ham shu struktura bo‘yicha qo‘shib beraman — istasangiz, aynan backend response sxemalarini yuboring, kontrakt bo‘yicha DTO’larni (models) aniq moslab chiqaman.
